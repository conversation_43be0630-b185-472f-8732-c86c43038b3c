// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Combate 3D Vertical Bridge Build Configuration
using UnrealBuildTool;
public class AuracronCombatBridge : ModuleRules
{
    public AuracronCombatBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
        PublicIncludePaths.AddRange(
            new string[] {
                // ... add public include paths required here ...
            }
        );
        PrivateIncludePaths.AddRange(
            new string[] {
                // ... add other private include paths required here ...
            }
        );
        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
                "CoreUObject",
                "Engine",
                "GameplayAbilities",
                "GameplayTags",
                "GameplayTasks",
                "ModularGameplay",
                "NetCore",
                "ReplicationGraph",
                "PhysicsCore",
                "ChaosCore",
                "ChaosSolverEngine",
                "NavigationSystem",
                "FieldSystemEngine",
                "EnhancedInput",
                "InputCore",
                "UMG",
                "Slate",
                "SlateCore",
                "DeveloperSettings",
                "EngineSettings",
                "AIModule",

                "StateTreeModule",

                "StructUtils",
                "MassEntity",
                "MassCommon",
                "ControlRig"
            }
        );
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "CoreUObject",
                "Engine",
                "Slate",
                "SlateCore",
                "RenderCore",
                "RHI",
                "NiagaraCore",
                "NiagaraShader",
                "NiagaraAnimNotifies",
                "AudioMixer",
                "MetasoundFrontend",
                "MetasoundStandardNodes",
                "SignalProcessing",
                "Json",
                "MeshDescription",
                "StaticMeshDescription",
                "GeometryCore",
                "DynamicMesh",
                "GeometryFramework",
                "InteractiveToolsFramework",
                "Chaos",
                "ChaosVehicles",
                "GeometryCollectionEngine",
                "Niagara",
                "NiagaraCore",
                "HTTP",
                "Analytics",
                "AnalyticsET",
                "Sockets",
                "Networking",

                "TraceLog",
                "ApplicationCore"
            }
        );
        
        // Editor-only dependencies
        if (Target.Type == TargetType.Editor)
        {
            PrivateDependencyModuleNames.AddRange(
                new string[]
                {
                    "ToolMenus",
                    "EditorStyle",
                    "EditorWidgets",
                    "UnrealEd",
                    "PropertyEditor",
                    "KismetCompiler",
                    "BlueprintGraph",
                    "Kismet",
                    "ToolMenus",
                    "EditorInteractiveToolsFramework",
                    "MeshModelingTools",
                    "MeshModelingToolsExp",
                    "CollisionAnalyzer",
                    "ControlRigDeveloper"
                }
            );
        }
        DynamicallyLoadedModuleNames.AddRange(
            new string[]
            {
                // ... add any modules that your module loads dynamically here ...
            }
        );
        // Enable optimization for shipping builds
        if (Target.Configuration == UnrealTargetConfiguration.Shipping)
        {
            OptimizeCode = CodeOptimization.InShippingBuildsOnly;
            bUseUnity = true;
        }
        // Enable additional features for development builds
        if (Target.Configuration == UnrealTargetConfiguration.Development || 
            Target.Configuration == UnrealTargetConfiguration.DebugGame)
        {
            PublicDefinitions.Add("AURACRON_COMBAT_DEBUG=1");
            PublicDefinitions.Add("AURACRON_COMBAT_PROFILING=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_COMBAT_DEBUG=0");
            PublicDefinitions.Add("AURACRON_COMBAT_PROFILING=0");
        }
        // Platform-specific configurations
        if (Target.Platform == UnrealTargetPlatform.Android || Target.Platform == UnrealTargetPlatform.IOS)
        {
            PublicDefinitions.Add("AURACRON_MOBILE_PLATFORM=1");
            // Mobile-specific optimizations
            bUseUnity = true;
            MinFilesUsingPrecompiledHeaderOverride = 1;
        }
        else
        {
            PublicDefinitions.Add("AURACRON_MOBILE_PLATFORM=0");
        }
        // UE 5.6 specific features
        PublicDefinitions.Add("AURACRON_UE56_FEATURES=1");
        PublicDefinitions.Add("WITH_CHAOS_PHYSICS=1");
        PublicDefinitions.Add("WITH_GAMEPLAY_ABILITY_SYSTEM=1");
        PublicDefinitions.Add("WITH_ENHANCED_INPUT=1");
        PublicDefinitions.Add("WITH_FIELD_SYSTEM=1");
        PublicDefinitions.Add("WITH_GEOMETRY_COLLECTION=1");
        PublicDefinitions.Add("WITH_AI_MODULE=1");
        PublicDefinitions.Add("WITH_BEHAVIOR_TREE=1");
        PublicDefinitions.Add("WITH_STATE_TREE=1");
        PublicDefinitions.Add("WITH_MASS_ENTITY=1");
        PublicDefinitions.Add("WITH_ANALYTICS=1");
        PublicDefinitions.Add("WITH_ADVANCED_DESTRUCTION=1");
        PublicDefinitions.Add("WITH_ELEMENTAL_SYSTEM=1");
        PublicDefinitions.Add("WITH_COMBO_SYSTEM=1");
    }
}


