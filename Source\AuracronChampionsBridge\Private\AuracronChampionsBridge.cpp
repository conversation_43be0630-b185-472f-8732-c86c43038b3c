// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Campeões Bridge Implementation

#include "AuracronChampionsBridge.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "GameFramework/Character.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "GameFramework/PlayerController.h"
#include "Components/SkeletalMeshComponent.h"
#include "Components/TimelineComponent.h"
#include "GameplayAbilities/Public/AbilitySystemComponent.h"
#include "GameplayAbilities/Public/AbilitySystemBlueprintLibrary.h"
#include "GameplayAbilities/Public/GameplayAbilitySpec.h"
#include "GameplayTagsManager.h"
#include "Animation/AnimInstance.h"
#include "Animation/AnimMontage.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Components/AudioComponent.h"
#include "Kismet/GameplayStatics.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "Engine/ActorChannel.h"
#include "DrawDebugHelpers.h"
#include "Async/Async.h"
#include "HAL/ThreadSafeBool.h"
#include "EnhancedInputComponent.h"
#include "EnhancedInputSubsystems.h"
#include "InputMappingContext.h"
#include "InputAction.h"

UAuracronChampionsBridge::UAuracronChampionsBridge()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 0.1f; // 10 FPS para otimização
    
    // Configurar replicação
    SetIsReplicatedByDefault(true);
    
    // Inicializar configurações padrão
    LoadDefaultChampionConfigurations();
}

void UAuracronChampionsBridge::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Inicializando Sistema de Campeões"));

    // Obter referências aos componentes
    if (AActor* Owner = GetOwner())
    {
        AbilitySystemComponent = UAbilitySystemBlueprintLibrary::GetAbilitySystemComponent(Owner);
        if (!AbilitySystemComponent)
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: AbilitySystemComponent não encontrado no Owner"));
        }

        // Obter Enhanced Input Component
        if (APawn* OwnerPawn = Cast<APawn>(Owner))
        {
            EnhancedInputComponent = Cast<UEnhancedInputComponent>(OwnerPawn->GetComponentByClass(UEnhancedInputComponent::StaticClass()));
            if (!EnhancedInputComponent)
            {
                UE_LOG(LogTemp, Warning, TEXT("AURACRON: EnhancedInputComponent não encontrado"));
            }
        }

        // Obter Character Movement Component
        if (ACharacter* OwnerCharacter = Cast<ACharacter>(Owner))
        {
            MovementComponent = OwnerCharacter->GetCharacterMovement();
            ChampionMesh = OwnerCharacter->GetMesh();
        }
    }

    // Inicializar sistema
    bSystemInitialized = InitializeChampionSystem();
    
    if (bSystemInitialized)
    {
        // Configurar timers para regeneração e cooldowns
        GetWorld()->GetTimerManager().SetTimer(
            RegenerationTimer,
            [this]()
            {
                ProcessRegeneration(1.0f); // A cada segundo
            },
            1.0f,
            true
        );

        GetWorld()->GetTimerManager().SetTimer(
            CooldownTimer,
            [this]()
            {
                ProcessCooldowns(0.1f); // A cada 100ms
            },
            0.1f,
            true
        );
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de Campeões inicializado com sucesso"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao inicializar Sistema de Campeões"));
    }
}

void UAuracronChampionsBridge::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Limpar timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(RegenerationTimer);
        GetWorld()->GetTimerManager().ClearTimer(CooldownTimer);
    }
    
    // Remover habilidades concedidas
    RemoveAbilities();
    
    // Limpar efeitos visuais
    for (UNiagaraComponent* Component : ActiveVisualEffects)
    {
        if (IsValid(Component))
        {
            Component->DestroyComponent();
        }
    }
    ActiveVisualEffects.Empty();

    Super::EndPlay(EndPlayReason);
}

void UAuracronChampionsBridge::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME(UAuracronChampionsBridge, ChampionConfigurations);
    DOREPLIFETIME(UAuracronChampionsBridge, SelectedChampionID);
    DOREPLIFETIME(UAuracronChampionsBridge, CurrentChampionState);
    DOREPLIFETIME(UAuracronChampionsBridge, CurrentAttributes);
}

void UAuracronChampionsBridge::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    if (!bSystemInitialized)
        return;

    // Processar regeneração contínua
    ProcessRegeneration(DeltaTime);
    
    // Processar cooldowns
    ProcessCooldowns(DeltaTime);
}

// === Core Champion Management ===

bool UAuracronChampionsBridge::SelectChampion(const FString& ChampionID)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema não inicializado"));
        return false;
    }

    if (ChampionID.IsEmpty())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: ChampionID vazio"));
        return false;
    }

    if (!ChampionConfigurations.Contains(ChampionID))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Campeão não encontrado: %s"), *ChampionID);
        return false;
    }

    // Find configuration by iterating through array
    const FAuracronChampionConfiguration* ConfigPtr = nullptr;
    for (const auto& Entry : ChampionConfigurations)
    {
        if (Entry.ChampionID == ChampionID)
        {
            ConfigPtr = &Entry.Configuration;
            break;
        }
    }

    if (!ConfigPtr)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Champion configuration not found for ID: %s"), *ChampionID);
        return false;
    }

    const FAuracronChampionConfiguration& Config = *ConfigPtr;
    if (!Config.bAvailableForSelection || !Config.bUnlocked)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Campeão não disponível para seleção: %s"), *ChampionID);
        return false;
    }

    // Remover configuração anterior se houver
    if (!SelectedChampionID.IsEmpty())
    {
        RemoveAbilities();
    }

    SelectedChampionID = ChampionID;
    CurrentAttributes = Config.BaseAttributes;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Campeão selecionado: %s"), *ChampionID);

    // Broadcast evento
    OnChampionSelected.Broadcast(ChampionID);

    return true;
}

bool UAuracronChampionsBridge::SpawnChampion(const FVector& SpawnLocation, const FRotator& SpawnRotation)
{
    if (!bSystemInitialized || SelectedChampionID.IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema não inicializado ou nenhum campeão selecionado"));
        return false;
    }

    if (bChampionSpawned)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Campeão já foi spawnado"));
        return false;
    }

    // Find configuration by iterating through array
    const FAuracronChampionConfiguration* Config = nullptr;
    for (const auto& Entry : ChampionConfigurations)
    {
        if (Entry.ChampionID == SelectedChampionID)
        {
            Config = &Entry.Configuration;
            break;
        }
    }
    if (!Config)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Configuração do campeão não encontrada"));
        return false;
    }

    // Configurar sistemas
    SetupAbilitySystem();
    SetupMovementSystem();
    SetupAnimationSystem();
    SetupChampionInput();

    // Aplicar configuração visual
    UpdateChampionMesh(Config->VisualConfig);

    // Aplicar atributos base
    ApplyBaseAttributes(Config->BaseAttributes);

    // Conceder habilidades
    GrantAbilities(Config->Abilities);

    // Teleportar para posição de spawn
    if (AActor* Owner = GetOwner())
    {
        Owner->SetActorLocation(SpawnLocation);
        Owner->SetActorRotation(SpawnRotation);
    }

    bChampionSpawned = true;
    CurrentChampionState = EAuracronChampionState::Idle;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Campeão spawnado: %s"), *SelectedChampionID);

    // Broadcast evento
    OnChampionSpawned.Broadcast(SelectedChampionID);

    return true;
}

bool UAuracronChampionsBridge::DespawnChampion()
{
    if (!bChampionSpawned)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Nenhum campeão spawnado"));
        return false;
    }

    // Remover habilidades
    RemoveAbilities();

    // Limpar efeitos visuais
    for (UNiagaraComponent* Component : ActiveVisualEffects)
    {
        if (IsValid(Component))
        {
            Component->DestroyComponent();
        }
    }
    ActiveVisualEffects.Empty();

    bChampionSpawned = false;
    CurrentChampionState = EAuracronChampionState::Idle;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Campeão despawnado: %s"), *SelectedChampionID);

    return true;
}

FAuracronChampionConfiguration UAuracronChampionsBridge::GetCurrentChampionConfiguration() const
{
    if (SelectedChampionID.IsEmpty())
    {
        return FAuracronChampionConfiguration();
    }

    // Find configuration by iterating through array
    const FAuracronChampionConfiguration* Config = nullptr;
    for (const auto& Entry : ChampionConfigurations)
    {
        if (Entry.ChampionID == SelectedChampionID)
        {
            Config = &Entry.Configuration;
            break;
        }
    }
    if (Config)
    {
        return *Config;
    }

    return FAuracronChampionConfiguration();
}

bool UAuracronChampionsBridge::IsChampionAlive() const
{
    return CurrentChampionState != EAuracronChampionState::Dead &&
           CurrentChampionState != EAuracronChampionState::Respawning &&
           CurrentAttributes.CurrentHealth > 0.0f;
}

// === Ability Management ===

bool UAuracronChampionsBridge::ActivateAbility(const FString& AbilitySlot)
{
    if (!bSystemInitialized || !AbilitySystemComponent)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema não inicializado ou ASC inválido"));
        return false;
    }

    if (!IsChampionAlive())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Campeão não está vivo"));
        return false;
    }

    // Find configuration by iterating through array
    const FAuracronChampionConfiguration* Config = nullptr;
    for (const auto& Entry : ChampionConfigurations)
    {
        if (Entry.ChampionID == SelectedChampionID)
        {
            Config = &Entry.Configuration;
            break;
        }
    }
    if (!Config)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Configuração do campeão não encontrada"));
        return false;
    }

    // Determinar qual habilidade ativar
    TSoftClassPtr<UGameplayAbility> AbilityClass;
    if (AbilitySlot == TEXT("Q"))
    {
        AbilityClass = Config->Abilities.QAbility;
    }
    else if (AbilitySlot == TEXT("W"))
    {
        AbilityClass = Config->Abilities.WAbility;
    }
    else if (AbilitySlot == TEXT("E"))
    {
        AbilityClass = Config->Abilities.EAbility;
    }
    else if (AbilitySlot == TEXT("R"))
    {
        AbilityClass = Config->Abilities.RAbility;
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Slot de habilidade inválido: %s"), *AbilitySlot);
        return false;
    }

    if (!AbilityClass.IsValid())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Habilidade não configurada para slot: %s"), *AbilitySlot);
        return false;
    }

    // Verificar cooldown
    if (!IsAbilityAvailable(AbilitySlot))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Habilidade em cooldown: %s"), *AbilitySlot);
        return false;
    }

    // Verificar mana
    float ManaCost = Config->Abilities.AbilityManaCosts.Contains(AbilitySlot) ?
                     Config->Abilities.AbilityManaCosts[AbilitySlot] : 0.0f;

    if (CurrentAttributes.CurrentMana < ManaCost)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Mana insuficiente para habilidade: %s"), *AbilitySlot);
        return false;
    }

    // Tentar ativar a habilidade
    TSubclassOf<UGameplayAbility> LoadedAbility = AbilityClass.LoadSynchronous();
    if (LoadedAbility)
    {
        bool bActivated = AbilitySystemComponent->TryActivateAbilityByClass(LoadedAbility);
        if (bActivated)
        {
            // Consumir mana
            CurrentAttributes.CurrentMana = FMath::Max(0.0f, CurrentAttributes.CurrentMana - ManaCost);

            UE_LOG(LogTemp, Log, TEXT("AURACRON: Habilidade ativada: %s"), *AbilitySlot);

            // Broadcast evento
            OnAbilityActivated.Broadcast(SelectedChampionID, AbilitySlot);

            return true;
        }
    }

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Falha ao ativar habilidade: %s"), *AbilitySlot);
    return false;
}

// === Level and Experience ===

bool UAuracronChampionsBridge::GainExperience(int32 ExperienceAmount)
{
    if (!bSystemInitialized || ExperienceAmount <= 0)
    {
        return false;
    }

    // Find configuration by iterating through array
    FAuracronChampionConfiguration* Config = nullptr;
    for (auto& Entry : ChampionConfigurations)
    {
        if (Entry.ChampionID == SelectedChampionID)
        {
            Config = &Entry.Configuration;
            break;
        }
    }
    if (!Config)
    {
        return false;
    }

    Config->CurrentExperience += ExperienceAmount;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Experiência ganha: %d (Total: %d)"), ExperienceAmount, Config->CurrentExperience);

    // Verificar se pode subir de nível
    while (Config->CurrentExperience >= Config->ExperienceToNextLevel && Config->ChampionLevel < 18)
    {
        LevelUp();
    }

    return true;
}

bool UAuracronChampionsBridge::LevelUp()
{
    // Find configuration by iterating through array
    FAuracronChampionConfiguration* Config = nullptr;
    for (auto& Entry : ChampionConfigurations)
    {
        if (Entry.ChampionID == SelectedChampionID)
        {
            Config = &Entry.Configuration;
            break;
        }
    }
    if (!Config || Config->ChampionLevel >= 18)
    {
        return false;
    }

    // Subir nível
    Config->ChampionLevel++;
    Config->CurrentExperience -= Config->ExperienceToNextLevel;

    // Calcular nova experiência necessária
    Config->ExperienceToNextLevel = 280 + (Config->ChampionLevel * 100); // Fórmula de progressão

    // Ganhar ponto de habilidade
    Config->Abilities.AvailableAbilityPoints++;

    // Recalcular atributos baseados no novo nível
    CurrentAttributes = CalculateAttributesForLevel(Config->ChampionLevel);

    // Aplicar novos atributos
    ApplyBaseAttributes(CurrentAttributes);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Campeão %s subiu para nível %d"), *SelectedChampionID, Config->ChampionLevel);

    // Broadcast evento
    OnChampionLevelUp.Broadcast(SelectedChampionID, Config->ChampionLevel);

    return true;
}

int32 UAuracronChampionsBridge::GetExperienceToNextLevel() const
{
    const FAuracronChampionConfigurationEntry* FoundEntry = ChampionConfigurations.FindByPredicate([&](const FAuracronChampionConfigurationEntry& Entry) {
        return Entry.ChampionID == SelectedChampionID;
    });
    
    const FAuracronChampionConfiguration* Config = FoundEntry ? &FoundEntry->Configuration : nullptr;
    if (!Config)
    {
        return 0;
    }

    return FMath::Max(0, Config->ExperienceToNextLevel - Config->CurrentExperience);
}

FAuracronChampionBaseAttributes UAuracronChampionsBridge::CalculateAttributesForLevel(int32 Level) const
{
    const FAuracronChampionConfigurationEntry* FoundEntry = ChampionConfigurations.FindByPredicate([&](const FAuracronChampionConfigurationEntry& Entry) {
        return Entry.ChampionID == SelectedChampionID;
    });
    
    if (!FoundEntry)
    {
        return FAuracronChampionBaseAttributes();
    }
    
    const FAuracronChampionConfiguration* Config = &FoundEntry->Configuration;

    FAuracronChampionBaseAttributes ScaledAttributes = Config->BaseAttributes;

    // Escalar atributos baseados no nível (fórmula de crescimento)
    float LevelMultiplier = 1.0f + ((Level - 1) * 0.1f); // +10% por nível

    ScaledAttributes.MaxHealth *= LevelMultiplier;
    ScaledAttributes.CurrentHealth = ScaledAttributes.MaxHealth; // HP cheio ao subir de nível
    ScaledAttributes.MaxMana *= (1.0f + ((Level - 1) * 0.05f)); // +5% mana por nível
    ScaledAttributes.CurrentMana = ScaledAttributes.MaxMana; // Mana cheia ao subir de nível
    ScaledAttributes.AttackDamage *= LevelMultiplier;
    ScaledAttributes.AbilityPower *= LevelMultiplier;
    ScaledAttributes.Armor += (Level - 1) * 3.0f; // +3 armadura por nível
    ScaledAttributes.MagicResistance += (Level - 1) * 2.5f; // +2.5 resistência mágica por nível

    return ScaledAttributes;
}

// === MetaHuman Integration ===

bool UAuracronChampionsBridge::ApplyMetaHumanConfiguration()
{
    if (!bSystemInitialized || !ChampionMesh)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema não inicializado ou ChampionMesh inválido"));
        return false;
    }

    const FAuracronChampionConfigurationEntry* FoundEntry = ChampionConfigurations.FindByPredicate([&](const FAuracronChampionConfigurationEntry& Entry) {
        return Entry.ChampionID == SelectedChampionID;
    });
    
    if (!FoundEntry || !FoundEntry->Configuration.VisualConfig.bUseMetaHuman)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: MetaHuman não configurado para este campeão"));
        return false;
    }
    
    const FAuracronChampionConfiguration* Config = &FoundEntry->Configuration;

    return UpdateChampionMesh(Config->VisualConfig);
}

bool UAuracronChampionsBridge::CustomizeFacialAppearance(const TMap<FString, float>& FacialParameters)
{
    if (!bSystemInitialized || !ChampionMesh)
    {
        return false;
    }

    const FAuracronChampionConfigurationEntry* FoundEntry = ChampionConfigurations.FindByPredicate([&](const FAuracronChampionConfigurationEntry& Entry) {
        return Entry.ChampionID == SelectedChampionID;
    });
    
    if (!FoundEntry || !FoundEntry->Configuration.VisualConfig.bAllowFacialCustomization)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Customização facial não permitida para este campeão"));
        return false;
    }
    
    const FAuracronChampionConfiguration* Config = &FoundEntry->Configuration;

    // Aplicar parâmetros faciais ao Control Rig
    if (Config->VisualConfig.FacialControlRig.IsValid())
    {
        // Load the Control Rig asset
        UControlRig* FacialControlRig = Config->VisualConfig.FacialControlRig.LoadSynchronous();
        if (FacialControlRig && ChampionMesh->GetAnimInstance())
        {
            // Get the skeletal mesh component's anim instance
            UAnimInstance* AnimInstance = ChampionMesh->GetAnimInstance();
            
            // Apply facial parameters through Control Rig
            for (const auto& Parameter : FacialParameters)
            {
                const FString& ParameterName = Parameter.Key;
                const float ParameterValue = Parameter.Value;
                
                // Find the control in the Control Rig
                FRigControlElement* ControlElement = FacialControlRig->GetHierarchy()->Find<FRigControlElement>(FRigElementKey(*ParameterName, ERigElementType::Control));
                if (ControlElement)
                {
                    // Set the control value
                    FRigControlValue ControlValue;
                    ControlValue.Set<float>(FMath::Clamp(ParameterValue, 0.0f, 1.0f));
                    FacialControlRig->GetHierarchy()->SetControlValue(ControlElement, ControlValue, ERigControlValueType::Current);
                    
                    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Parâmetro facial aplicado - %s: %f"), *ParameterName, ParameterValue);
                }
                else
                {
                    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Controle facial não encontrado: %s"), *ParameterName);
                }
            }
            
            // Execute the Control Rig to apply changes using UE 5.6 API
#if WITH_EDITOR
            // FacialControlRig->Execute(FRigUnit_BeginExecution::EventName);
#endif
            // Alternative execution for runtime
            if (FacialControlRig)
            {
                FacialControlRig->RequestInit();
                FacialControlRig->Evaluate_AnyThread();
            }
            
            UE_LOG(LogTemp, Log, TEXT("AURACRON: %d parâmetros faciais aplicados via Control Rig"), FacialParameters.Num());
            return true;
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao carregar Control Rig facial ou AnimInstance inválida"));
        }
    }

    return false;
}

bool UAuracronChampionsBridge::ApplyAlternativeSkin(int32 SkinIndex)
{
    if (!bSystemInitialized || !ChampionMesh)
    {
        return false;
    }

    const FAuracronChampionConfigurationEntry* FoundEntry = ChampionConfigurations.FindByPredicate([&](const FAuracronChampionConfigurationEntry& Entry) {
        return Entry.ChampionID == SelectedChampionID;
    });
    
    if (!FoundEntry || !FoundEntry->Configuration.VisualConfig.bSupportsAlternativeSkins)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Skins alternativas não suportadas para este campeão"));
        return false;
    }
    
    const FAuracronChampionConfiguration* Config = &FoundEntry->Configuration;

    if (!Config->VisualConfig.AlternativeSkins.IsValidIndex(SkinIndex))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Índice de skin inválido: %d"), SkinIndex);
        return false;
    }

    // Aplicar skin alternativa
    UTexture2D* SkinTexture = Config->VisualConfig.AlternativeSkins[SkinIndex].LoadSynchronous();
    if (SkinTexture && ChampionMesh)
    {
        // Create a dynamic material instance for the skin
        UMaterialInterface* BaseMaterial = ChampionMesh->GetMaterial(0);
        if (BaseMaterial)
        {
            UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, this);
            if (DynamicMaterial)
            {
                // Set the alternative skin texture
                DynamicMaterial->SetTextureParameterValue(TEXT("BaseColorTexture"), SkinTexture);
                DynamicMaterial->SetTextureParameterValue(TEXT("DiffuseTexture"), SkinTexture);
                DynamicMaterial->SetTextureParameterValue(TEXT("AlbedoTexture"), SkinTexture);
                
                // Apply the material to all material slots
                for (int32 MaterialIndex = 0; MaterialIndex < ChampionMesh->GetNumMaterials(); ++MaterialIndex)
                {
                    ChampionMesh->SetMaterial(MaterialIndex, DynamicMaterial);
                }
                
                // If there are additional textures for this skin (normal, roughness, etc.)
                if (Config->VisualConfig.AlternativeSkinNormals.IsValidIndex(SkinIndex))
                {
                    UTexture2D* NormalTexture = Config->VisualConfig.AlternativeSkinNormals[SkinIndex].LoadSynchronous();
                    if (NormalTexture)
                    {
                        DynamicMaterial->SetTextureParameterValue(TEXT("NormalTexture"), NormalTexture);
                        DynamicMaterial->SetTextureParameterValue(TEXT("NormalMap"), NormalTexture);
                    }
                }
                
                if (Config->VisualConfig.AlternativeSkinRoughness.IsValidIndex(SkinIndex))
                {
                    UTexture2D* RoughnessTexture = Config->VisualConfig.AlternativeSkinRoughness[SkinIndex].LoadSynchronous();
                    if (RoughnessTexture)
                    {
                        DynamicMaterial->SetTextureParameterValue(TEXT("RoughnessTexture"), RoughnessTexture);
                        DynamicMaterial->SetTextureParameterValue(TEXT("SpecularTexture"), RoughnessTexture);
                    }
                }
                
                UE_LOG(LogTemp, Log, TEXT("AURACRON: Skin alternativa aplicada com sucesso: %d"), SkinIndex);
                return true;
            }
            else
            {
                UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao criar material dinâmico para skin alternativa"));
            }
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: Material base não encontrado no ChampionMesh"));
        }
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao carregar textura da skin alternativa ou ChampionMesh inválido"));
    }

    return false;
}

bool UAuracronChampionsBridge::ResetToDefaultAppearance()
{
    if (!bSystemInitialized)
    {
        return false;
    }

    const FAuracronChampionConfigurationEntry* FoundEntry = ChampionConfigurations.FindByPredicate([&](const FAuracronChampionConfigurationEntry& Entry) {
        return Entry.ChampionID == SelectedChampionID;
    });
    
    if (!FoundEntry)
    {
        return false;
    }
    
    const FAuracronChampionConfiguration* Config = &FoundEntry->Configuration;

    // Resetar para configuração visual padrão
    return UpdateChampionMesh(Config->VisualConfig);
}

// === Input Management ===

bool UAuracronChampionsBridge::SetupChampionInput()
{
    if (!EnhancedInputComponent)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: EnhancedInputComponent não disponível"));
        return false;
    }

    const FAuracronChampionConfigurationEntry* FoundEntry = ChampionConfigurations.FindByPredicate([&](const FAuracronChampionConfigurationEntry& Entry) {
        return Entry.ChampionID == SelectedChampionID;
    });
    
    if (!FoundEntry)
    {
        return false;
    }
    
    const FAuracronChampionConfiguration* Config = &FoundEntry->Configuration;

    // Configurar Input Mapping Context
    if (Config->InputConfig.InputMappingContext.IsValid())
    {
        UInputMappingContext* MappingContext = Config->InputConfig.InputMappingContext.LoadSynchronous();
        if (MappingContext)
        {
            if (APlayerController* PC = Cast<APlayerController>(GetOwner()->GetInstigatorController()))
            {
                if (UEnhancedInputLocalPlayerSubsystem* Subsystem = ULocalPlayer::GetSubsystem<UEnhancedInputLocalPlayerSubsystem>(PC->GetLocalPlayer()))
                {
                    Subsystem->AddMappingContext(MappingContext, 1);
                    UE_LOG(LogTemp, Log, TEXT("AURACRON: Input Mapping Context configurado"));
                }
            }
        }
    }

    // Configurar bindings de input
    // Movimento
    if (Config->InputConfig.MoveAction.IsValid())
    {
        UInputAction* MoveAction = Config->InputConfig.MoveAction.LoadSynchronous();
        if (MoveAction)
        {
            EnhancedInputComponent->BindAction(MoveAction, ETriggerEvent::Triggered, this, &UAuracronChampionsBridge::ProcessMovementInput);
        }
    }

    // Look
    if (Config->InputConfig.LookAction.IsValid())
    {
        UInputAction* LookAction = Config->InputConfig.LookAction.LoadSynchronous();
        if (LookAction)
        {
            EnhancedInputComponent->BindAction(LookAction, ETriggerEvent::Triggered, this, &UAuracronChampionsBridge::ProcessLookInput);
        }
    }

    // Habilidades
    if (Config->InputConfig.QAbilityAction.IsValid())
    {
        UInputAction* QAction = Config->InputConfig.QAbilityAction.LoadSynchronous();
        if (QAction)
        {
            EnhancedInputComponent->BindAction(QAction, ETriggerEvent::Started, this, &UAuracronChampionsBridge::ActivateQAbility);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Input do campeão configurado"));
    return true;
}

void UAuracronChampionsBridge::ActivateQAbility()
{
    ActivateAbility(TEXT("Q"));
}

void UAuracronChampionsBridge::ProcessLookInput(const FInputActionValue& Value)
{
    // Extrair o valor 2D do input
    const FVector2D LookVector = Value.Get<FVector2D>();
    
    // Obter o Character owner
    if (ACharacter* Character = Cast<ACharacter>(GetOwner()))
    {
        // Aplicar rotação baseada no input
        Character->AddControllerYawInput(LookVector.X);
        Character->AddControllerPitchInput(LookVector.Y);
    }
}

void UAuracronChampionsBridge::ProcessMovementInput(const FInputActionValue& Value)
{
    // Extrair o valor 2D do input
    const FVector2D MovementVector = Value.Get<FVector2D>();
    
    // Obter o Character owner
    if (ACharacter* Character = Cast<ACharacter>(GetOwner()))
    {
        // Aplicar movimento baseado no input
        Character->AddMovementInput(Character->GetActorForwardVector(), MovementVector.Y);
        Character->AddMovementInput(Character->GetActorRightVector(), MovementVector.X);
    }
}
