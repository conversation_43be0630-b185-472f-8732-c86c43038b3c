#pragma once

#include "CoreMinimal.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/CriticalSection.h"
#include "Templates/UniquePtr.h"
#include "Containers/Array.h"
#include "Math/Vector.h"
#include "Math/Vector2D.h"
#include "Math/Color.h"
#include "Engine/Texture2D.h"
#include "Engine/TextureRenderTarget2D.h"
#include "Materials/Material.h"
#include "Materials/MaterialInstance.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Materials/MaterialParameterCollection.h"
#include "RenderTargetPool.h"
#include "CanvasTypes.h"
#include "Engine/Canvas.h"
#include "DSP/FloatArrayMath.h"
#include "DSP/FFTAlgorithm.h"

#include "AuracronTextureGeneration.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogAuracronTextureGeneration, Log, All);

// Enums for texture generation
UENUM(BlueprintType)
enum class ETextureQuality : uint8
{
    Low             UMETA(DisplayName = "Low (512x512)"),
    Medium          UMETA(DisplayName = "Medium (1024x1024)"),
    High            UMETA(DisplayName = "High (2048x2048)"),
    Ultra           UMETA(DisplayName = "Ultra (4096x4096)"),
    Custom          UMETA(DisplayName = "Custom Resolution")
};

UENUM(BlueprintType)
enum class EAuracronTextureType : uint8
{
    Diffuse         UMETA(DisplayName = "Diffuse"),
    Normal          UMETA(DisplayName = "Normal"),
    Roughness       UMETA(DisplayName = "Roughness"),
    Metallic        UMETA(DisplayName = "Metallic"),
    Specular        UMETA(DisplayName = "Specular"),
    Emissive        UMETA(DisplayName = "Emissive"),
    Opacity         UMETA(DisplayName = "Opacity"),
    Height          UMETA(DisplayName = "Height"),
    AmbientOcclusion UMETA(DisplayName = "Ambient Occlusion"),
    Subsurface      UMETA(DisplayName = "Subsurface")
};

UENUM(BlueprintType)
enum class ENoiseType : uint8
{
    Perlin          UMETA(DisplayName = "Perlin"),
    Simplex         UMETA(DisplayName = "Simplex"),
    Worley          UMETA(DisplayName = "Worley"),
    FBM             UMETA(DisplayName = "Fractal Brownian Motion"),
    Ridged          UMETA(DisplayName = "Ridged Multifractal"),
    Billow          UMETA(DisplayName = "Billow")
};

UENUM(BlueprintType)
enum class ETextureBlendMode : uint8
{
    Normal          UMETA(DisplayName = "Normal"),
    Multiply        UMETA(DisplayName = "Multiply"),
    Screen          UMETA(DisplayName = "Screen"),
    Overlay         UMETA(DisplayName = "Overlay"),
    SoftLight       UMETA(DisplayName = "Soft Light"),
    HardLight       UMETA(DisplayName = "Hard Light"),
    ColorDodge      UMETA(DisplayName = "Color Dodge"),
    ColorBurn       UMETA(DisplayName = "Color Burn"),
    Darken          UMETA(DisplayName = "Darken"),
    Lighten         UMETA(DisplayName = "Lighten"),
    Difference      UMETA(DisplayName = "Difference"),
    Exclusion       UMETA(DisplayName = "Exclusion")
};

// Structures for texture generation
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FNoiseParameters
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise")
    ENoiseType NoiseType = ENoiseType::Perlin;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise")
    float Frequency = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise")
    float Amplitude = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise")
    int32 Octaves = 4;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise")
    float Lacunarity = 2.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise")
    float Persistence = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise")
    FVector2D Offset = FVector2D::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise")
    int32 Seed = 0;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FSkinVariationData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skin Variation")
    float PoreSize = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skin Variation")
    float PoreIntensity = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skin Variation")
    float WrinkleIntensity = 0.3f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skin Variation")
    float FreckleIntensity = 0.2f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skin Variation")
    float BlemishIntensity = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skin Variation")
    FLinearColor BaseColor = FLinearColor(0.8f, 0.6f, 0.5f, 1.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skin Variation")
    FNoiseParameters NoiseParams;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FTattooData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tattoo")
    FString TattooName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tattoo")
    TArray<FVector2D> TattooPath;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tattoo")
    float LineWidth = 0.01f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tattoo")
    FLinearColor TattooColor = FLinearColor::Black;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tattoo")
    float Opacity = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tattoo")
    float BlurRadius = 0.0f;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FScarData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scar")
    TArray<FVector2D> ScarPath;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scar")
    float ScarWidth = 0.005f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scar")
    float ScarDepth = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scar")
    FLinearColor ScarColor = FLinearColor(0.9f, 0.7f, 0.6f, 1.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scar")
    float Roughness = 0.8f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scar")
    float EdgeSoftness = 0.1f;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FMakeupData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Makeup")
    FLinearColor LipstickColor = FLinearColor::Red;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Makeup")
    float LipstickIntensity = 0.8f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Makeup")
    FLinearColor EyeshadowColor = FLinearColor::Blue;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Makeup")
    float EyeshadowIntensity = 0.6f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Makeup")
    FLinearColor BlushColor = FLinearColor(1.0f, 0.5f, 0.5f, 1.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Makeup")
    float BlushIntensity = 0.4f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Makeup")
    float FoundationCoverage = 0.7f;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FAgingEffectData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Aging")
    float WrinkleIntensity = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Aging")
    float AgeSpotIntensity = 0.3f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Aging")
    float SkinSaggingIntensity = 0.2f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Aging")
    float ColorVariationIntensity = 0.4f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Aging")
    int32 AgeYears = 30;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FTextureGenerationParameters
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture Generation")
    EAuracronTextureType TextureType = EAuracronTextureType::Diffuse;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture Generation")
    ETextureQuality Quality = ETextureQuality::High;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture Generation")
    FIntPoint CustomResolution = FIntPoint(2048, 2048);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture Generation")
    FSkinVariationData SkinVariation;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture Generation")
    TArray<FTattooData> Tattoos;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture Generation")
    TArray<FScarData> Scars;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture Generation")
    FMakeupData Makeup;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture Generation")
    FAgingEffectData AgingEffects;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture Generation")
    bool bGenerateMipmaps = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture Generation")
    bool bSRGB = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture Generation")
    int32 Seed = 0;
};

/**
 * Texture generation system for MetaHuman Bridge
 * Provides advanced procedural texture generation with UE5.6 rendering APIs
 */
class AURACRONMETAHUMANBRIDGE_API FAuracronTextureGeneration
{
public:
    FAuracronTextureGeneration();
    ~FAuracronTextureGeneration();

    // Core texture generation
    UTexture2D* GenerateProceduralTexture(const FTextureGenerationParameters& Parameters);
    bool GenerateTextureToRenderTarget(const FTextureGenerationParameters& Parameters, UTextureRenderTarget2D* RenderTarget);
    UTexture2D* CreateTextureFromRenderTarget(UTextureRenderTarget2D* RenderTarget, const FString& TextureName);

    // Specialized texture generation
    UTexture2D* GenerateSkinDiffuseTexture(const FSkinVariationData& SkinData, ETextureQuality Quality);
    UTexture2D* GenerateSkinNormalTexture(const FSkinVariationData& SkinData, ETextureQuality Quality);
    UTexture2D* GenerateSkinRoughnessTexture(const FSkinVariationData& SkinData, ETextureQuality Quality);
    UTexture2D* GenerateSkinSubsurfaceTexture(const FSkinVariationData& SkinData, ETextureQuality Quality);

    // Texture modification
    bool ApplyTattoos(UTexture2D* BaseTexture, const TArray<FTattooData>& Tattoos);
    bool ApplyScars(UTexture2D* BaseTexture, const TArray<FScarData>& Scars);
    bool ApplyMakeup(UTexture2D* BaseTexture, const FMakeupData& Makeup);
    bool ApplyAgingEffects(UTexture2D* BaseTexture, const FAgingEffectData& AgingEffects);

    // Texture blending and compositing
    UTexture2D* BlendTextures(UTexture2D* BaseTexture, UTexture2D* OverlayTexture, ETextureBlendMode BlendMode, float Opacity);
    bool CompositeTextures(const TArray<UTexture2D*>& Textures, const TArray<ETextureBlendMode>& BlendModes, const TArray<float>& Opacities, UTexture2D*& OutCompositeTexture);

    // Material parameter collection updates
    bool UpdateMaterialParameterCollection(UMaterialParameterCollection* ParameterCollection, const FTextureGenerationParameters& Parameters);

    // Texture validation and optimization
    bool ValidateTextureGenerationParameters(const FTextureGenerationParameters& Parameters, FString& OutErrorMessage) const;
    void ClearTextureCache();
    void GetTextureGenerationStats(int32& OutTotalTextures, int64& OutMemoryUsage, float& OutGenerationTime) const;

    // Thread safety
    mutable FCriticalSection TextureGenerationMutex;

private:
    // Texture cache for performance
    mutable TMap<FString, TWeakObjectPtr<UTexture2D>> TextureCache;
    mutable int64 TextureCacheMemoryUsage;
    mutable float TotalGenerationTime;

    // Internal texture generation methods
    UTexture2D* CreateTransientTexture(const FIntPoint& Resolution, EPixelFormat PixelFormat, const FString& TextureName) const;
    bool FillTextureData(UTexture2D* Texture, const TArray<FColor>& ColorData) const;
    
    // Noise generation methods
    float GenerateNoiseValue(const FVector2D& Coordinates, const FNoiseParameters& NoiseParams) const;
    float GeneratePerlinNoise(const FVector2D& Coordinates, const FNoiseParameters& NoiseParams) const;
    float GenerateSimplexNoise(const FVector2D& Coordinates, const FNoiseParameters& NoiseParams) const;
    float GenerateWorleyNoise(const FVector2D& Coordinates, const FNoiseParameters& NoiseParams) const;
    float GenerateFractalBrownianMotion(const FVector2D& Coordinates, const FNoiseParameters& NoiseParams) const;

    // Color blending methods
    FLinearColor BlendColors(const FLinearColor& Base, const FLinearColor& Overlay, ETextureBlendMode BlendMode, float Opacity) const;
    FLinearColor ApplyColorCorrection(const FLinearColor& Color, float Contrast, float Brightness, const FLinearColor& ColorMultiplier) const;

    // Pattern generation methods
    float GeneratePorePattern(const FVector2D& UV, const FSkinVariationData& SkinData) const;
    float GenerateTattooPattern(const FVector2D& UV, const FTattooData& TattooData) const;
    float GenerateScarPattern(const FVector2D& UV, const FScarData& ScarData) const;
    float GenerateMakeupPattern(const FVector2D& UV, const FMakeupData& MakeupData) const;
    float GenerateAgingPattern(const FVector2D& UV, const FAgingEffectData& AgingData) const;

    // Utility methods
    FIntPoint GetQualityResolution(ETextureQuality Quality) const;
    float DistanceToLineSegment(const FVector2D& Point, const FVector2D& LineStart, const FVector2D& LineEnd) const;
    void ApplyGaussianBlur(TArray<FColor>& TextureData, const FIntPoint& Resolution, float BlurRadius) const;
    
    void UpdateTextureCacheStats() const;
    void CleanupTextureCache();
    bool IsValidTextureResolution(const FIntPoint& Resolution) const;
    EPixelFormat GetPixelFormatForQuality(ETextureQuality Quality) const;

    // Prevent copying
    FAuracronTextureGeneration(const FAuracronTextureGeneration&) = delete;
    FAuracronTextureGeneration& operator=(const FAuracronTextureGeneration&) = delete;
};
