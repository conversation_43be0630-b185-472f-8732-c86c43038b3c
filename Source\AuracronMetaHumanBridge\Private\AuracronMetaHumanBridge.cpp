#include "AuracronMetaHumanBridge.h"
#include "Modules/ModuleManager.h"
#include "Interfaces/IPluginManager.h"
#include "Misc/Paths.h"
#include "HAL/PlatformFilemanager.h"
#include "GenericPlatform/GenericPlatformFile.h"
#include "Async/Async.h"
#include "Engine/Engine.h"
#include "Framework/Notifications/NotificationManager.h"
#include "Widgets/Notifications/SNotificationList.h"

// Include all refactored modules
#include "AuracronDNAReaderWriter.h"
#include "AuracronBehaviorReader.h"
#include "AuracronDNACalib.h"
#include "AuracronMeshDeformation.h"
#include "AuracronRigTransformation.h"
#include "AuracronTextureGeneration.h"
#include "AuracronHairGeneration.h"
#include "AuracronClothingGeneration.h"
#include "AuracronEyeGeneration.h"
#include "AuracronAnimationBlueprint.h"
#include "AuracronErrorHandling.h"
#include "AuracronPerformanceOptimization.h"

DEFINE_LOG_CATEGORY(LogAuracronMetaHumanBridge);

#define LOCTEXT_NAMESPACE "FAuracronMetaHumanBridgeModule"

//////////////////////////////////////////////////////////////////////////
// FAuracronMetaHumanBridgeModule Implementation
//////////////////////////////////////////////////////////////////////////

void FAuracronMetaHumanBridgeModule::StartupModule()
{
    UE_LOG(LogAuracronMetaHumanBridge, Log, TEXT("AuracronMetaHumanBridge module starting up"));

    try
    {
        // Initialize all refactored modules using UE5.6 module system
        DNAReaderWriter = MakeShared<FAuracronDNAReaderWriter>();
        BehaviorReader = MakeShared<FAuracronBehaviorReader>();
        DNACalib = MakeShared<FAuracronDNACalib>();
        MeshDeformation = MakeShared<FAuracronMeshDeformation>();
        RigTransformation = MakeShared<FAuracronRigTransformation>();
        TextureGeneration = MakeShared<FAuracronTextureGeneration>();
        HairGeneration = MakeShared<FAuracronHairGeneration>();
        ClothingGeneration = MakeShared<FAuracronClothingGeneration>();
        EyeGeneration = MakeShared<FAuracronEyeGeneration>();
        AnimationBlueprint = MakeShared<FAuracronAnimationBlueprint>();
        ErrorHandling = MakeShared<FAuracronErrorHandling>();
        PerformanceOptimization = MakeShared<FAuracronPerformanceOptimization>();

        // Initialize performance optimization system
        FPerformanceOptimizationConfiguration PerfConfig;
        PerfConfig.bEnablePerformanceMonitoring = true;
        PerfConfig.bEnableAutomaticOptimizations = true;
        PerfConfig.bEnableMemoryOptimization = true;
        PerfConfig.bEnableCPUOptimization = true;
        PerfConfig.bEnableGPUOptimization = true;
        PerfConfig.MaxMemoryUsagePercent = 80.0f;
        PerfConfig.MaxCPUUsagePercent = 85.0f;
        PerfConfig.OptimizationCheckInterval = 5.0f;
        
        if (!PerformanceOptimization->InitializePerformanceOptimization(PerfConfig))
        {
            UE_LOG(LogAuracronMetaHumanBridge, Warning, TEXT("Failed to initialize performance optimization"));
        }

        // Initialize error handling system
        ErrorHandling->SetErrorHandlingEnabled(true);
        ErrorHandling->SetAutoRecoveryEnabled(true);
        ErrorHandling->SetMaxRetryAttempts(3);

        // Validate system state
        if (!ErrorHandling->ValidateSystemState())
        {
            UE_LOG(LogAuracronMetaHumanBridge, Warning, TEXT("System validation detected potential issues"));
        }

        UE_LOG(LogAuracronMetaHumanBridge, Log, TEXT("All AuracronMetaHumanBridge modules initialized successfully"));
        
        // Report successful startup
        if (ErrorHandling.IsValid())
        {
            ErrorHandling->ReportError(EErrorSeverity::Info, TEXT("ModuleStartup"), 
                                     TEXT("AuracronMetaHumanBridge module started successfully"), 
                                     TEXT("STARTUP_SUCCESS"), false, false);
        }
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronMetaHumanBridge, Error, TEXT("Exception during module startup: %s"), UTF8_TO_TCHAR(e.what()));
        
        // Report critical error using error handling system
        if (ErrorHandling.IsValid())
        {
            ErrorHandling->ReportError(EErrorSeverity::Critical, TEXT("ModuleStartup"), 
                                     FString::Printf(TEXT("Module startup failed: %s"), UTF8_TO_TCHAR(e.what())), 
                                     TEXT("STARTUP_FAILURE"), true, false);
        }
    }
}

void FAuracronMetaHumanBridgeModule::ShutdownModule()
{
    UE_LOG(LogAuracronMetaHumanBridge, Log, TEXT("AuracronMetaHumanBridge module shutting down"));

    try
    {
        // Report shutdown start
        if (ErrorHandling.IsValid())
        {
            ErrorHandling->ReportError(EErrorSeverity::Info, TEXT("ModuleShutdown"), 
                                     TEXT("AuracronMetaHumanBridge module shutting down"), 
                                     TEXT("SHUTDOWN_START"), false, false);
        }

        // Shutdown all modules in reverse order
        if (PerformanceOptimization.IsValid())
        {
            PerformanceOptimization->ShutdownPerformanceOptimization();
            PerformanceOptimization.Reset();
        }

        if (ErrorHandling.IsValid())
        {
            ErrorHandling->FlushErrorReports();
            ErrorHandling.Reset();
        }

        if (AnimationBlueprint.IsValid())
        {
            AnimationBlueprint->ClearAnimBlueprintCache();
            AnimationBlueprint.Reset();
        }

        if (EyeGeneration.IsValid())
        {
            EyeGeneration.Reset();
        }

        if (ClothingGeneration.IsValid())
        {
            ClothingGeneration->ClearClothingAssetCache();
            ClothingGeneration.Reset();
        }

        if (HairGeneration.IsValid())
        {
            HairGeneration->ClearHairAssetCache();
            HairGeneration.Reset();
        }

        if (TextureGeneration.IsValid())
        {
            TextureGeneration->ClearTextureCache();
            TextureGeneration.Reset();
        }

        if (RigTransformation.IsValid())
        {
            RigTransformation.Reset();
        }

        if (MeshDeformation.IsValid())
        {
            MeshDeformation.Reset();
        }

        if (DNACalib.IsValid())
        {
            DNACalib.Reset();
        }

        if (BehaviorReader.IsValid())
        {
            BehaviorReader.Reset();
        }

        if (DNAReaderWriter.IsValid())
        {
            DNAReaderWriter.Reset();
        }

        UE_LOG(LogAuracronMetaHumanBridge, Log, TEXT("All AuracronMetaHumanBridge modules shutdown successfully"));
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronMetaHumanBridge, Error, TEXT("Exception during module shutdown: %s"), UTF8_TO_TCHAR(e.what()));
    }
}

// ========================================
// Module Accessors
// ========================================

TSharedPtr<FAuracronDNAReaderWriter> FAuracronMetaHumanBridgeModule::GetDNAReaderWriter() const
{
    return DNAReaderWriter;
}

TSharedPtr<FAuracronBehaviorReader> FAuracronMetaHumanBridgeModule::GetBehaviorReader() const
{
    return BehaviorReader;
}

TSharedPtr<FAuracronDNACalib> FAuracronMetaHumanBridgeModule::GetDNACalib() const
{
    return DNACalib;
}

TSharedPtr<FAuracronMeshDeformation> FAuracronMetaHumanBridgeModule::GetMeshDeformation() const
{
    return MeshDeformation;
}

TSharedPtr<FAuracronRigTransformation> FAuracronMetaHumanBridgeModule::GetRigTransformation() const
{
    return RigTransformation;
}

TSharedPtr<FAuracronTextureGeneration> FAuracronMetaHumanBridgeModule::GetTextureGeneration() const
{
    return TextureGeneration;
}

TSharedPtr<FAuracronHairGeneration> FAuracronMetaHumanBridgeModule::GetHairGeneration() const
{
    return HairGeneration;
}

TSharedPtr<FAuracronClothingGeneration> FAuracronMetaHumanBridgeModule::GetClothingGeneration() const
{
    return ClothingGeneration;
}

TSharedPtr<FAuracronEyeGeneration> FAuracronMetaHumanBridgeModule::GetEyeGeneration() const
{
    return EyeGeneration;
}

TSharedPtr<FAuracronAnimationBlueprint> FAuracronMetaHumanBridgeModule::GetAnimationBlueprint() const
{
    return AnimationBlueprint;
}

TSharedPtr<FAuracronErrorHandling> FAuracronMetaHumanBridgeModule::GetErrorHandling() const
{
    return ErrorHandling;
}

TSharedPtr<FAuracronPerformanceOptimization> FAuracronMetaHumanBridgeModule::GetPerformanceOptimization() const
{
    return PerformanceOptimization;
}

// ========================================
// Utility Methods
// ========================================

bool FAuracronMetaHumanBridgeModule::IsModuleReady() const
{
    return DNAReaderWriter.IsValid() && 
           BehaviorReader.IsValid() && 
           DNACalib.IsValid() && 
           MeshDeformation.IsValid() && 
           RigTransformation.IsValid() && 
           TextureGeneration.IsValid() && 
           HairGeneration.IsValid() && 
           ClothingGeneration.IsValid() && 
           EyeGeneration.IsValid() && 
           AnimationBlueprint.IsValid() && 
           ErrorHandling.IsValid() && 
           PerformanceOptimization.IsValid();
}

FString FAuracronMetaHumanBridgeModule::GetModuleVersion() const
{
    return TEXT("2.0.0");
}

FString FAuracronMetaHumanBridgeModule::GetModuleDescription() const
{
    return TEXT("Auracron MetaHuman Bridge - Advanced MetaHuman DNA manipulation and generation system for UE5.6");
}

TArray<FString> FAuracronMetaHumanBridgeModule::GetAvailableModules() const
{
    TArray<FString> Modules;
    Modules.Add(TEXT("DNAReaderWriter"));
    Modules.Add(TEXT("BehaviorReader"));
    Modules.Add(TEXT("DNACalib"));
    Modules.Add(TEXT("MeshDeformation"));
    Modules.Add(TEXT("RigTransformation"));
    Modules.Add(TEXT("TextureGeneration"));
    Modules.Add(TEXT("HairGeneration"));
    Modules.Add(TEXT("ClothingGeneration"));
    Modules.Add(TEXT("EyeGeneration"));
    Modules.Add(TEXT("AnimationBlueprint"));
    Modules.Add(TEXT("ErrorHandling"));
    Modules.Add(TEXT("PerformanceOptimization"));
    return Modules;
}

#undef LOCTEXT_NAMESPACE

IMPLEMENT_MODULE(FAuracronMetaHumanBridgeModule, AuracronMetaHumanBridge)
